import { useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import useAuthStore from '../store/authStore';

/**
 * Custom hook for authentication management
 * Provides authentication state and methods with navigation integration
 */
const useAuth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const {
    user,
    loading,
    error,
    isAuthenticated,
    initialized,
    initialize,
    login,
    logout,
    register,
    verifyOtp,
    resendOtp,
    fetchCurrentUser,
    updateUser,
    hasRole,
    hasAnyRole
  } = useAuthStore();

  // Initialize authentication on mount
  useEffect(() => {
    if (!initialized) {
      initialize();
    }
  }, [initialized, initialize]);

  /**
   * Enhanced login with navigation
   * @param {Object} credentials - Login credentials
   * @param {string} redirectTo - Where to redirect after successful login
   */
  const loginWithRedirect = useCallback(async (credentials, redirectTo) => {
    const result = await login(credentials);
    
    if (result && result.user) {
      // Determine redirect path
      const targetPath = redirectTo || 
                        location.state?.from?.pathname || 
                        getDashboardPath(result.user.role);
      
      navigate(targetPath, { replace: true });
      return result;
    }
    
    return result;
  }, [login, navigate, location.state]);

  /**
   * Enhanced logout with navigation
   * @param {string} redirectTo - Where to redirect after logout (default: '/login')
   */
  const logoutWithRedirect = useCallback(async (redirectTo = '/login') => {
    await logout();
    navigate(redirectTo, { replace: true });
  }, [logout, navigate]);

  /**
   * Enhanced registration with navigation
   * @param {Object} userData - Registration data
   * @param {string} redirectTo - Where to redirect after successful registration
   */
  const registerWithRedirect = useCallback(async (userData, redirectTo = '/verify-otp') => {
    const result = await register(userData);
    
    if (result && !result.error) {
      navigate(redirectTo, { 
        state: { email: userData.email },
        replace: true 
      });
      return result;
    }
    
    return result;
  }, [register, navigate]);

  /**
   * Enhanced OTP verification with navigation
   * @param {Object} otpData - OTP verification data
   * @param {string} redirectTo - Where to redirect after successful verification
   */
  const verifyOtpWithRedirect = useCallback(async (otpData, redirectTo) => {
    const result = await verifyOtp(otpData);
    
    if (result && result.user) {
      const targetPath = redirectTo || getDashboardPath(result.user.role);
      navigate(targetPath, { replace: true });
      return result;
    }
    
    return result;
  }, [verifyOtp, navigate]);

  /**
   * Check if current route is accessible for user
   * @param {string|string[]} requiredRoles - Required roles for the route
   * @returns {boolean} Whether user can access the route
   */
  const canAccessRoute = useCallback((requiredRoles) => {
    if (!isAuthenticated) return false;
    if (!requiredRoles) return true;
    
    const rolesArray = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    return hasAnyRole(rolesArray);
  }, [isAuthenticated, hasAnyRole]);

  /**
   * Redirect user to appropriate dashboard based on role
   */
  const redirectToDashboard = useCallback(() => {
    if (user) {
      const dashboardPath = getDashboardPath(user.role);
      navigate(dashboardPath, { replace: true });
    }
  }, [user, navigate]);

  /**
   * Check authentication and redirect if needed
   * Useful for components that need to ensure user is authenticated
   */
  const requireAuth = useCallback((redirectTo = '/login') => {
    if (initialized && !isAuthenticated) {
      navigate(redirectTo, { 
        state: { from: location },
        replace: true 
      });
      return false;
    }
    return isAuthenticated;
  }, [initialized, isAuthenticated, navigate, location]);

  /**
   * Check role and redirect if user doesn't have required role
   * @param {string|string[]} requiredRoles - Required roles
   * @param {string} redirectTo - Where to redirect if role check fails
   */
  const requireRole = useCallback((requiredRoles, redirectTo) => {
    if (!canAccessRoute(requiredRoles)) {
      const fallbackRedirect = redirectTo || getDashboardPath(user?.role) || '/';
      navigate(fallbackRedirect, { 
        state: { 
          from: location,
          error: 'Access denied. Insufficient permissions.'
        },
        replace: true 
      });
      return false;
    }
    return true;
  }, [canAccessRoute, user, navigate, location]);

  return {
    // State
    user,
    loading,
    error,
    isAuthenticated,
    initialized,
    
    // Basic auth methods
    initialize,
    fetchCurrentUser,
    updateUser,
    resendOtp,
    
    // Enhanced methods with navigation
    login: loginWithRedirect,
    logout: logoutWithRedirect,
    register: registerWithRedirect,
    verifyOtp: verifyOtpWithRedirect,
    
    // Role checking
    hasRole,
    hasAnyRole,
    canAccessRoute,
    
    // Navigation helpers
    redirectToDashboard,
    requireAuth,
    requireRole,
    
    // Utility
    isAdmin: user?.role === 'admin',
    isCompany: user?.role === 'company',
    isStudent: user?.role === 'student',
  };
};

/**
 * Get the appropriate dashboard path based on user role
 * @param {string} role - User role (admin, company, student)
 * @returns {string} Dashboard path
 */
const getDashboardPath = (role) => {
  switch (role) {
    case 'admin':
      return '/admin-dashboard';
    case 'company':
      return '/company-dashboard';
    case 'student':
      return '/dashboard';
    default:
      return '/';
  }
};

export default useAuth;
