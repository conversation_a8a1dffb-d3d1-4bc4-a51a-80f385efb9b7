<<<<<<< HEAD
import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
=======
import React, { useState, createContext, useMemo } from "react";
import { BrowserRouter as Router, Routes, Route, Outlet, Navigate } from "react-router-dom";
>>>>>>> 9b75d168268f72244d6016b3e609e647f9b359fe
import { Toaster } from 'react-hot-toast';
import './styles/layout.css';

// General Components
import Navbar from "./Components/Navbar.jsx";
import Hero from "./Components/Hero.jsx";
import AnimatedCardsSection from "./Components/AnimatedCardsSection.jsx";
import About from "./Components/About.jsx";
import HowItWorks from "./Components/HowItWorks.jsx";
import Testimonials from "./Components/Testimonials.jsx";
import Footer from "./Components/Footer.jsx";
<<<<<<< HEAD

// Layout
=======
>>>>>>> 9b75d168268f72244d6016b3e609e647f9b359fe
import Layout from "./Components/Layout.jsx";

// Protected Route Components
import ProtectedRoute from "./components/auth/ProtectedRoute.jsx";
import { AdminRoute, CompanyRoute, StudentRoute } from "./components/auth/RoleBasedRoute.jsx";

// Company Components
import CompanyDashboard from "./Components/company/CompanyDashboard.jsx";
import CreateJob from './Components/company/Createjob.jsx';
import TestManagement from "./Components/company/TestManagement.jsx";
import Aptitude from "./Components/company/Aptitude.jsx";
import Interview from "./Components/company/Interview.jsx";
import Profile from "./Components/company/Profile.jsx";
<<<<<<< HEAD

// Admin Components (Each has its own file)
import AdminDashboard from "./Components/admin/AdminDashboard.jsx";
import AdminJobPosts from "./Components/admin/AdminJobPosts.jsx";
import AdminCompanies from "./Components/admin/AdminCompanies.jsx";
import AdminUsers from "./Components/admin/AdminUsers.jsx";
import AdminSettings from "./Components/admin/AdminSettings.jsx";

// Student Components
import StudentDashboard from "./Components/Dashboard/Studentdashboard.jsx";
import Test from "./Components/company/components/TestInterface.jsx";
import TestResult from "./Components/Dashboard/Quiz.jsx"; // Using Quiz component for test results
import InterviewPrep from "./Components/Dashboard/interview.jsx";
import StudentProfile from "./Components/Dashboard/Profile.jsx";

// Demo Components
import AuthDemo from "./components/demo/AuthDemo.jsx";

// Auth Pages
import RegistrationPage from "./pages/RegistationPage.jsx";
import VerifyOtp from "./pages/VerifyOtp.jsx";
import LoginPage from "./pages/LoginPage.jsx";

// Landing Page
=======
import Test from "./Components/company/test.jsx";
import AdminDashboard from "./Components/admin/AdminDashboard.jsx";
import AdminUsers from "./Components/admin/AdminUsers.jsx";
import AdminCompanies from "./Components/admin/AdminCompanies.jsx";
import AdminJobPosts from "./Components/admin/AdminJobPosts.jsx";
import AdminSettings from "./Components/admin/AdminSettings.jsx";
import LoginPage from "./pages/LoginPage.jsx";
import StudentLayout from "./pages/student/components/StudentLayout.jsx";
import StudentHome from "./pages/student/Home.jsx";
import StudentProfile from "./pages/student/Profile.jsx";
import StudentJobs from "./pages/student/Jobs.jsx";
import StudentApplications from "./pages/student/Applications.jsx";
import StudentTests from "./pages/student/Tests.jsx";
import StudentTestDetails from "./pages/student/TestDetails.jsx";
import StudentResults from "./pages/student/Results.jsx";
import JobDetails from "./pages/student/JobDetails.jsx";
import JobsCategory from "./pages/student/JobsCategory.jsx";
import ApplicationDetails from "./pages/student/applications/ApplicationDetails.jsx";
import Courses from "./pages/student/applications/Courses.jsx";
import Certificates from "./pages/student/applications/Certificates.jsx";
import Resume from "./pages/student/Resume.jsx";
import axios from 'axios';

const API = axios.create({
  baseURL: 'https://resumebuilder-m27v.onrender.com',
});

API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const SharedDataContext = React.createContext();
export const ProfileContext = createContext();

const initialProfile = {
  name: 'Abhishek E B',
  email: 'gabbisheik5297',
  address: 'Alva’s Institute of Engineering and Technology, Karnataka',
  avatar: '',
  about: '',
  resume: '',
  skills: [],
  work: [],
  education: [],
  responsibilities: [],
  certificates: [],
  projects: [],
  achievements: [],
  social: [
    { icon: '', url: '' }
  ],
  streaks: [],
};

const sectionKeys = [
  'about', 'resume', 'skills', 'work', 'education', 'responsibilities', 'certificates', 'projects', 'achievements'
];

function ProfileProvider({ children }) {
  const [profile, setProfile] = useState(initialProfile);
  const profileCompletion = useMemo(() => {
    const filledSections = sectionKeys.filter(key => {
      const val = profile[key];
      if (Array.isArray(val)) return val.length > 0;
      return !!val && val !== '';
    }).length;
    return Math.round((filledSections / sectionKeys.length) * 100);
  }, [profile]);
  return (
    <ProfileContext.Provider value={{ profile, setProfile, profileCompletion }}>
      {children}
    </ProfileContext.Provider>
  );
}

>>>>>>> 9b75d168268f72244d6016b3e609e647f9b359fe
function LandingPage() {
  return (
    <div className="font-sans text-gray-800 bg-[#f7f8fa]">
      <Navbar />
      <main>
        <section className="min-h-[85vh] flex items-center justify-center bg-white">
          <Hero />
        </section>
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto bg-white">
            <AnimatedCardsSection />
          </div>
        </section>
        <section className="py-20 px-4 bg-white">
          <About />
        </section>
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <HowItWorks />
          </div>
        </section>
        <section className="py-20 px-4">
          <Testimonials dark={false} />
        </section>
      </main>
      <Footer />
    </div>
  );
}

function App() {
  return (
<<<<<<< HEAD
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />

        {/* Demo Route - For testing authentication */}
        <Route path="/auth-demo" element={<AuthDemo />} />

        {/* Auth Routes - Redirect to dashboard if already logged in */}
        <Route
          path="/register"
          element={
            <ProtectedRoute requireAuth={false}>
              <RegistrationPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/verify-otp"
          element={
            <ProtectedRoute requireAuth={false}>
              <VerifyOtp />
            </ProtectedRoute>
          }
        />
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <LoginPage />
            </ProtectedRoute>
          }
        />

        {/* Admin Routes - Only accessible by admin role */}
        <Route
          path="/admin-dashboard"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/job-posts"
          element={
            <AdminRoute>
              <AdminJobPosts />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/companies"
          element={
            <AdminRoute>
              <AdminCompanies />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/users"
          element={
            <AdminRoute>
              <AdminUsers />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/settings"
          element={
            <AdminRoute>
              <AdminSettings />
            </AdminRoute>
          }
        />

        {/* Company Routes under Layout - Only accessible by company role */}
        <Route
          path="/"
          element={
            <CompanyRoute>
              <Layout />
            </CompanyRoute>
          }
        >
          <Route path="dashboard" element={<CompanyDashboard />} />
          <Route path="job-create" element={<CreateJob />} />
          <Route path="test-management" element={<TestManagement />} />
          <Route path="aptitude" element={<Aptitude />} />
          <Route path="interview" element={<Interview />} />
          <Route path="profile" element={<Profile />} />
        </Route>

        {/* Student Routes - Only accessible by student role */}
        <Route
          path="/student-dashboard"
          element={
            <StudentRoute>
              <StudentDashboard />
            </StudentRoute>
          }
        />
        <Route
          path="/test"
          element={
            <StudentRoute>
              <Test />
            </StudentRoute>
          }
        />
        <Route
          path="/test-result"
          element={
            <StudentRoute>
              <TestResult />
            </StudentRoute>
          }
        />
        <Route
          path="/interview-prep"
          element={
            <StudentRoute>
              <InterviewPrep />
            </StudentRoute>
          }
        />
        <Route
          path="/student-profile"
          element={
            <StudentRoute>
              <StudentProfile />
            </StudentRoute>
          }
        />

        {/* Catch all route - redirect to home */}
        <Route
          path="*"
          element={<Navigate to="/" replace />}
        />
      </Routes>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
            borderRadius: '10px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
          },
          success: {
            style: { background: '#10B981' },
            iconTheme: { primary: '#fff', secondary: '#10B981' },
          },
          error: {
            style: { background: '#EF4444' },
            iconTheme: { primary: '#fff', secondary: '#EF4444' },
          },
          loading: {
            style: { background: '#3B82F6' },
          },
        }}
      />
    </Router>
=======
    <SharedDataContext.Provider value={{}}>
      <ProfileProvider>
        <Router>
          <Routes>
            <Route path="/" element={<StudentLayout />}>
              <Route index element={<StudentHome />} />
              <Route path="home" element={<StudentHome />} />
              <Route path="profile" element={<StudentProfile />} />
              <Route path="resume" element={<Resume />} />
              <Route path="jobs" element={<StudentJobs />} />
              <Route path="jobs/category/:categoryName" element={<JobsCategory />} />
              <Route path="jobs/:id" element={<JobDetails />} />
              <Route path="applications" element={<StudentApplications />} />
              <Route path="applications/:id" element={<ApplicationDetails />} />
              <Route path="tests" element={<StudentTests />} />
              <Route path="tests/:testid" element={<StudentTestDetails />} />
              <Route path="results" element={<StudentResults />} />
              <Route path="courses" element={<Courses />} />
              <Route path="certificates" element={<Certificates />} />
            </Route>
            <Route path="/student/profile" element={<Navigate to="/profile" replace />} />
            <Route path="/student/home" element={<Navigate to="/home" replace />} />
            <Route path="/student/jobs" element={<Navigate to="/jobs" replace />} />
            <Route path="/student/applications" element={<Navigate to="/applications" replace />} />
            <Route path="/student/tests" element={<Navigate to="/tests" replace />} />
            <Route path="/student/results" element={<Navigate to="/results" replace />} />
            <Route path="/student/courses" element={<Navigate to="/courses" replace />} />
            <Route path="/student/certificates" element={<Navigate to="/certificates" replace />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/admin-dashboard" element={<AdminDashboard />}>
              <Route path="users" element={<AdminUsers />} />
              <Route path="companies" element={<AdminCompanies />} />
              <Route path="job-posts" element={<AdminJobPosts />} />
              <Route path="settings" element={<AdminSettings />} />
            </Route>
            <Route path="/dashboard" element={<CompanyDashboard />} />
            <Route path="/dashboard/job-create" element={<CreateJob />} />
            <Route path="/dashboard/aptitude" element={<Aptitude />} />
            <Route path="/dashboard/test" element={<Test />} />
            <Route path="/dashboard/interview" element={<Interview />} />
            <Route path="/dashboard/profile" element={<Profile />} />
          </Routes>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 3000,
              style: {
                background: '#363636',
                color: '#fff',
                borderRadius: '10px',
                padding: '16px',
                fontSize: '14px',
                fontWeight: '500',
              },
              success: {
                style: {
                  background: '#10B981',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#10B981',
                },
              },
              error: {
                style: {
                  background: '#EF4444',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#EF4444',
                },
              },
              loading: {
                style: {
                  background: '#3B82F6',
                },
              },
            }}
          />
        </Router>
      </ProfileProvider>
    </SharedDataContext.Provider>
>>>>>>> 9b75d168268f72244d6016b3e609e647f9b359fe
  );
}

export default App;
