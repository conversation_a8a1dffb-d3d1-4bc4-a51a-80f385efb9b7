import React, { useContext } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { FaUser, FaBriefcase, FaClipboardList, FaFlask, FaChartBar, FaHome } from 'react-icons/fa';
import { ProfileContext } from '../../../App';

const links = [
  { to: '/home', label: 'Home', icon: <FaHome /> },
  { to: '/profile', label: 'Profile', icon: <FaUser /> },
  { to: '/jobs', label: 'Jobs', icon: <FaBriefcase /> },
  { to: '/applications', label: 'Applications', icon: <FaClipboardList /> },
  { to: '/tests', label: 'Tests', icon: <FaFlask /> },
  { to: '/results', label: 'Results', icon: <FaChartBar /> },
];

const Header = () => {
  const profileContext = useContext(ProfileContext);
  const profile = profileContext?.profile || {};
  const initials = profile.name ? profile.name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U';
  const navigate = useNavigate();
  return (
    <header className="fixed top-0 left-0 w-full h-16 bg-[#23414b] shadow flex items-center justify-between px-8 z-40">
      <div className="text-xl font-bold text-white">Student Portal</div>
            {/* Removed navigation bar */}
      <div className="flex items-center gap-6">
        <div
          className="w-10 h-10 rounded-full bg-[#23414b]/80 flex items-center justify-center font-bold text-white overflow-hidden cursor-pointer"
          onClick={() => navigate('/student/profile')}
          title="View Profile"
        >
          {profile.avatar ? (
            <img src={profile.avatar} alt="avatar" className="w-full h-full object-cover rounded-full" />
          ) : (
            initials
          )}
        </div>
        <button className="bg-white text-[#23414b] px-4 py-2 rounded font-semibold hover:bg-[#23414b]/10 transition">Logout</button>
      </div>
    </header>
  );
};

export default Header; 