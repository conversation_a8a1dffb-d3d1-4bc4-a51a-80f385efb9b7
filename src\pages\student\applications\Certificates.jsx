import React, { useEffect, useState, useRef, useContext } from 'react';
import { ProfileContext } from '../../../App';

const courses = [
  { id: 'java', title: 'Java Programming' },
  { id: 'c', title: 'C Programming' },
  { id: 'python', title: 'Python Programming' },
];

export default function Certificates() {
  const [completed, setCompleted] = useState({});
  const certRefs = useRef({});
  const profileContext = useContext(ProfileContext);
  const profile = profileContext?.profile || {};
  const userName = profile.name || 'Student Name';
  const userEmail = profile.email || '<EMAIL>';

  useEffect(() => {
    const load = () => {
      const stored = localStorage.getItem('completedCourses');
      setCompleted(stored ? JSON.parse(stored) : {});
    };
    load();
    window.addEventListener('courseCompleted', load);
    return () => window.removeEventListener('courseCompleted', load);
  }, []);

  const completedCourses = courses.filter(c => completed[c.id]);

  const handlePrint = (id) => {
    const printContents = certRefs.current[id].innerHTML;
    const win = window.open('', '', 'width=900,height=700');
    win.document.write(`
      <html>
        <head>
          <title>Certificate</title>
          <style>
            body { background: #f0f4f8; }
            .certificate {
              width: 700px;
              margin: 40px auto;
              padding: 40px 32px;
              background: #fff;
              border: 8px solid #23414c;
              border-radius: 24px;
              box-shadow: 0 8px 32px rgba(35,65,76,0.12);
              font-family: 'Georgia', serif;
              color: #23414c;
              text-align: center;
            }
            .cert-title { font-size: 2.5rem; font-weight: bold; margin-bottom: 1.5rem; }
            .cert-course { font-size: 1.5rem; font-weight: 600; margin: 1.5rem 0 0.5rem; }
            .cert-name { font-size: 1.25rem; font-weight: 500; margin-bottom: 1.5rem; }
            .cert-footer { margin-top: 2.5rem; font-size: 1rem; color: #23414c; }
            .cert-logo { width: 80px; margin-bottom: 1.5rem; }
            .cert-email { font-size: 1rem; color: #23414c; margin-bottom: 1.5rem; }
          </style>
        </head>
        <body>${printContents}</body>
      </html>
    `);
    win.document.close();
    win.focus();
    setTimeout(() => win.print(), 500);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-[#23414c] mb-8">Certificates</h1>
        {completedCourses.length === 0 ? (
          <div className="bg-white rounded-xl shadow p-8 text-gray-400 text-center">
            No certificates yet. Complete a course to unlock certificates!
          </div>
        ) : (
          <div className="grid gap-10">
            {completedCourses.map(course => (
              <div key={course.id} className="flex flex-col items-center gap-4">
                <div ref={el => certRefs.current[course.id] = el}>
                  <div className="certificate" style={{background:'#fff',border:'8px solid #23414c',borderRadius:24,padding:40,maxWidth:700,margin:'0 auto',boxShadow:'0 8px 32px rgba(35,65,76,0.12)',fontFamily:'Georgia,serif',color:'#23414c',textAlign:'center'}}>
                    <img src="https://upload.wikimedia.org/wikipedia/commons/6/6b/Unstop_logo.png" alt="Logo" className="cert-logo" style={{width:80,marginBottom:24}} />
                    <div className="cert-title">Certificate of Completion</div>
                    <div>This is to certify that</div>
                    <div className="cert-name" style={{fontWeight:600,fontSize:'1.25rem',margin:'12px 0'}}>{userName}</div>
                    <div className="cert-email" style={{fontSize:'1rem',color:'#23414c',marginBottom:'1.5rem'}}>{userEmail}</div>
                    <div>has successfully completed the course</div>
                    <div className="cert-course" style={{fontWeight:700,fontSize:'1.5rem',margin:'16px 0'}}>{course.title}</div>
                    <div className="cert-footer" style={{marginTop:40,fontSize:'1rem'}}>Date: {new Date().toLocaleDateString()}<br/>Medini Student Portal</div>
                  </div>
                </div>
                <button className="bg-[#23414c] text-white px-4 py-2 rounded-full font-semibold shadow hover:bg-[#23414c]/90 transition" onClick={() => handlePrint(course.id)}>
                  Download/Print Certificate
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 