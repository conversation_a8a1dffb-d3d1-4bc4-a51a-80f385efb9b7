import React, { useState } from 'react';
import { FaSearch, FaShareAlt, FaCheckCircle, FaBriefcase, FaBook, FaCertificate } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import useStudent from '../../hooks/useStudent';

const tabs = [
  'All',

];

const mockApplications = [
  {
    id: 1,
    title: 'Direct Sales Executive',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    registeredOn: '2025-01-07T18:45:00Z',
    deadline: '2025-01-10T07:44:00Z',
    status: 'Closed',
    steps: ['Registration Form', 'Extended Form'],
    completedSteps: [true, true],
    notifications: false,
    completed: true,
    email: '<EMAIL>',
    category: 'Jobs',
  },
  {
    id: 2,
    title: 'Python Internship',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    registeredOn: '2024-09-26T22:26:00Z',
    deadline: '2024-11-15T23:59:00Z',
    status: 'Closed',
    steps: ['Registration Form', 'Extended Form'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Internships',
  },
  {
    id: 3,
    title: 'MEDINI Hiring Challenge 2024',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    registeredOn: '2024-09-13T16:23:00Z',
    deadline: '2024-09-19T23:59:00Z',
    status: 'Closed',
    steps: ['Registration Form', 'Face Verification'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Competitions',
  },
  {
    id: 4,
    title: 'MEDINI Imagination Challenge 2024: Student Track',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    registeredOn: '2024-08-25T19:39:00Z',
    deadline: '2024-09-09T23:59:00Z',
    status: 'Closed',
    steps: ['Registration Form', 'Face Verification'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Competitions',
  },
];

const filterOptions = ['All', 'Live', 'Closed'];
const sortOptions = ['Newest', 'Oldest'];

const formatDate = (iso) => {
  const d = new Date(iso);
  return d.toLocaleString('en-GB', { day: '2-digit', month: 'short', year: '2-digit', hour: '2-digit', minute: '2-digit', hour12: true }) + ' IST';
};

const Applications = () => {
  const [activeTab, setActiveTab] = useState('All');
  const [search, setSearch] = useState('');
  const [apps, setApps] = useState(mockApplications);
  const [filter, setFilter] = useState('All');
  const [sort, setSort] = useState('Newest');
  const navigate = useNavigate();
  const { profile, profileCompletion } = useStudent();

  const handleToggleNotification = (id) => {
    setApps(apps => apps.map(app => app.id === id ? { ...app, notifications: !app.notifications } : app));
  };

  // Filter and sort
  let filteredApps = apps.filter(app => {
    const matchesTab = activeTab === 'All' || app.category === activeTab;
    const matchesSearch = app.title.toLowerCase().includes(search.toLowerCase());
    const matchesFilter = filter === 'All' || app.status === filter;
    return matchesTab && matchesSearch && matchesFilter;
  });
  filteredApps = filteredApps.sort((a, b) => {
    if (sort === 'Newest') {
      return new Date(b.registeredOn) - new Date(a.registeredOn);
    } else {
      return new Date(a.registeredOn) - new Date(b.registeredOn);
    }
  });

  return (
    <div className="flex flex-col md:flex-row min-h-screen bg-gray-50">
      {/* User Info Sidebar */}
      <aside className="hidden md:flex flex-col w-72 bg-white border-r py-8 px-4 z-30 min-h-screen">
        <div className="flex flex-col items-center mb-6">
          <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-2xl font-bold text-gray-700 mb-2 overflow-hidden">
            {profile.avatar ? (
              <img src={profile.avatar} alt="avatar" className="w-full h-full object-cover rounded-full" />
            ) : (
              profile.name.split(' ').map(n => n[0]).join('').toUpperCase()
            )}
          </div>
          <div className="font-bold text-lg text-gray-800">{profile.name}</div>
          <div className="text-sm text-gray-500 mb-2">{profile.email}</div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
            <div className="bg-green-500 h-2 rounded-full" style={{ width: `${profileCompletion || 0}%` }}></div>
          </div>
          <div className="text-xs text-gray-500 mb-2">{profileCompletion || 0}%</div>
        </div>
        <div className="mb-4">
          <div className="text-xs font-bold text-gray-400 uppercase mb-2">For Users</div>
          <nav className="flex flex-col gap-1">
            <a href="/applications" className="flex items-center gap-3 px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-[#23414c]/10 text-[#23414c] font-bold">
              <span className="text-lg"><FaBriefcase /></span>
              <span>Registrations/Applications</span>
            </a>
            <a href="/courses" className="flex items-center gap-3 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-[#23414c]/10 text-[#23414c]/80">
              <span className="text-lg"><FaBook /></span>
              <span>Courses</span>
            </a>
            <a href="/certificates" className="flex items-center gap-3 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-[#23414c]/10 text-[#23414c]/80">
              <span className="text-lg"><FaCertificate /></span>
              <span>Certificates</span>
            </a>
          </nav>
        </div>
      </aside>
      <main className="flex-1 p-4 md:p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6 text-blue-900">Applications</h1>
          {/* Tabs */}
          <div className="flex gap-4 border-b mb-4 overflow-x-auto">
            {tabs.map(tab => (
              <button
                key={tab}
                className={`pb-2 px-2 font-semibold whitespace-nowrap border-b-2 transition-all ${activeTab === tab ? 'border-blue-700 text-blue-700' : 'border-transparent text-gray-500 hover:text-blue-700'}`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </button>
            ))}
          </div>
          {/* Filter and Sort Buttons */}
          <div className="flex items-center gap-2 mb-6 mt-2">
            {filterOptions.map(opt => (
              <button
                key={opt}
                className={`px-5 py-2 rounded border font-semibold transition-all ${filter === opt ? 'border-blue-700 text-blue-700 bg-white shadow-sm' : 'border-gray-300 text-gray-700 bg-white hover:border-blue-400'}`}
                onClick={() => setFilter(opt)}
              >
                {opt}
              </button>
            ))}
            <div className="relative">
              <select
                value={sort}
                onChange={e => setSort(e.target.value)}
                className="appearance-none px-5 py-2 rounded border font-semibold border-gray-300 text-gray-700 bg-white focus:border-blue-700 focus:text-blue-700 cursor-pointer"
                style={{ minWidth: 110 }}
              >
                {sortOptions.map(opt => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </select>
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">▼</span>
            </div>
          </div>
          {/* Search */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <input
                type="text"
                placeholder="Search your registrations!"
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none"
              />
              <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            </div>
          </div>
          {/* Application Cards */}
          <div className="flex flex-col gap-4">
            {filteredApps.map(app => (
              <div
                key={app.id}
                className="bg-white rounded-xl shadow p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4 cursor-pointer hover:shadow-lg transition"
                onClick={() => navigate(`/student/applications/${app.id}`)}
              >
                <div className="flex items-center gap-4 flex-1">
                  <img src={app.logo} alt={app.company} className="w-16 h-16 rounded bg-white border object-contain" />
                  <div>
                    <div className="font-bold text-lg text-gray-800">{app.title}</div>
                    <div className="text-gray-500 text-sm">Registered on: {formatDate(app.registeredOn)} <span className="mx-2">|</span> By: <span className="font-semibold">{app.email}</span></div>
                    <div className="text-gray-500 text-sm">Deadline: <span className="font-semibold">{formatDate(app.deadline)}</span> <span className="ml-2 px-2 py-0.5 rounded bg-red-100 text-red-700 text-xs">{app.status}</span></div>
                  </div>
                </div>
                {/* Progress Steps with connecting lines */}
                <div className="flex flex-col items-center gap-2 md:gap-4">
                  <div className="flex gap-2 items-center">
                    {app.steps.map((step, idx) => (
                      <React.Fragment key={step}>
                        <div className="flex flex-col items-center">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${app.completedSteps[idx] ? 'bg-green-500' : 'bg-gray-300'}`}>
                            <FaCheckCircle className="text-white" />
                          </div>
                          <div className="text-xs text-gray-600 mt-1 whitespace-nowrap">{step}</div>
                        </div>
                        {idx < app.steps.length - 1 && (
                          <div className="w-8 h-1 bg-green-400 rounded-full mx-1 mt-2" />
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                </div>
                {/* Notification Toggle and Status */}
                <div className="flex flex-col items-end gap-2 min-w-[140px]">
                  <div className="flex items-center gap-2">
                    <button className="text-gray-400 hover:text-blue-700" onClick={e => e.stopPropagation()}><FaShareAlt /></button>
                    <span className={`font-semibold text-sm flex items-center gap-1 ${app.completed ? 'text-green-600' : 'text-gray-400'}`}>{app.completed && <FaCheckCircle />} {app.completed ? 'Completed' : 'In Progress'}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
      {/* Remove Sidebar below main content on mobile, left on desktop */}
      {/* <div className="block md:fixed md:left-0 md:top-0 md:h-full md:w-72 md:block w-full md:relative z-30 order-last md:order-none">
        <Sidebar />
      </div> */}
    </div>
  );
};

export default Applications; 