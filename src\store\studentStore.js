import { create } from 'zustand';
import axios from 'axios';

// Create axios instance with interceptors
const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include token in headers
axiosInstance.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

const initialProfile = {
    name: '',
    email: '',
    address: '',
    avatar: '',
    about: '',
    resume: '',
    skills: [],
    work: [],
    education: [],
    responsibilities: [],
    certificates: [],
    projects: [],
    achievements: [],
    social: [
        { icon: '', url: '' }
    ],
    streaks: [],
};

const sectionKeys = [
    'about', 'resume', 'skills', 'work', 'education', 'responsibilities', 'certificates', 'projects', 'achievements'
];

const useStudentStore = create((set, get) => ({
    // Profile state
    profile: initialProfile,
    loading: false,
    error: null,
    
    // Applications state
    applications: [],
    
    // Tests state
    tests: [],
    testResults: [],
    
    // Jobs state
    jobs: [],
    savedJobs: [],
    
    // Computed profile completion
    get profileCompletion() {
        const { profile } = get();
        const filledSections = sectionKeys.filter(key => {
            const val = profile[key];
            if (Array.isArray(val)) return val.length > 0;
            return !!val && val !== '';
        }).length;
        return Math.round((filledSections / sectionKeys.length) * 100);
    },

    // Profile actions
    updateProfile: (profileData) => {
        set({ profile: { ...get().profile, ...profileData } });
    },

    setProfile: (profile) => {
        set({ profile });
    },

    // Fetch profile from backend
    fetchProfile: async () => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.get('/api/student/profile');
            set({ profile: response.data.profile, loading: false });
            return response.data.profile;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to fetch profile', loading: false });
            return null;
        }
    },

    // Save profile to backend
    saveProfile: async (profileData) => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.put('/api/student/profile', profileData);
            set({ profile: response.data.profile, loading: false });
            return response.data.profile;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to save profile', loading: false });
            return null;
        }
    },

    // Applications actions
    fetchApplications: async () => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.get('/api/student/applications');
            set({ applications: response.data.applications, loading: false });
            return response.data.applications;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to fetch applications', loading: false });
            return null;
        }
    },

    applyToJob: async (jobId, applicationData) => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.post(`/api/student/jobs/${jobId}/apply`, applicationData);
            // Refresh applications
            get().fetchApplications();
            set({ loading: false });
            return response.data;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to apply to job', loading: false });
            return null;
        }
    },

    // Tests actions
    fetchTests: async () => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.get('/api/student/tests');
            set({ tests: response.data.tests, loading: false });
            return response.data.tests;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to fetch tests', loading: false });
            return null;
        }
    },

    fetchTestResults: async () => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.get('/api/student/test-results');
            set({ testResults: response.data.results, loading: false });
            return response.data.results;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to fetch test results', loading: false });
            return null;
        }
    },

    submitTest: async (testId, answers) => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.post(`/api/student/tests/${testId}/submit`, { answers });
            // Refresh test results
            get().fetchTestResults();
            set({ loading: false });
            return response.data;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to submit test', loading: false });
            return null;
        }
    },

    // Jobs actions
    fetchJobs: async (filters = {}) => {
        set({ loading: true, error: null });
        try {
            const response = await axiosInstance.get('/api/student/jobs', { params: filters });
            set({ jobs: response.data.jobs, loading: false });
            return response.data.jobs;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to fetch jobs', loading: false });
            return null;
        }
    },

    saveJob: async (jobId) => {
        try {
            await axiosInstance.post(`/api/student/jobs/${jobId}/save`);
            set({ savedJobs: [...get().savedJobs, jobId] });
            return true;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to save job' });
            return false;
        }
    },

    unsaveJob: async (jobId) => {
        try {
            await axiosInstance.delete(`/api/student/jobs/${jobId}/save`);
            set({ savedJobs: get().savedJobs.filter(id => id !== jobId) });
            return true;
        } catch (error) {
            set({ error: error.response?.data?.message || 'Failed to unsave job' });
            return false;
        }
    },

    // Clear error
    clearError: () => {
        set({ error: null });
    },

    // Reset store
    reset: () => {
        set({
            profile: initialProfile,
            applications: [],
            tests: [],
            testResults: [],
            jobs: [],
            savedJobs: [],
            loading: false,
            error: null,
        });
    },
}));

export default useStudentStore;
