import { useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import useStudentStore from '../store/studentStore';
import useAuth from './useAuth';

/**
 * Custom hook for student-specific functionality
 * Provides student data management and navigation helpers
 */
const useStudent = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isStudent } = useAuth();
  
  const {
    // State
    profile,
    applications,
    tests,
    testResults,
    jobs,
    savedJobs,
    loading,
    error,
    profileCompletion,
    
    // Actions
    updateProfile,
    setProfile,
    fetchProfile,
    saveProfile,
    fetchApplications,
    applyToJob,
    fetchTests,
    fetchTestResults,
    submitTest,
    fetchJobs,
    saveJob,
    unsaveJob,
    clearError,
    reset,
  } = useStudentStore();

  // Initialize student data when component mounts
  useEffect(() => {
    if (isStudent && user) {
      // Initialize profile with user data if available
      if (user.name || user.email) {
        updateProfile({
          name: user.name || profile.name,
          email: user.email || profile.email,
        });
      }
    }
  }, [isStudent, user, updateProfile, profile.name, profile.email]);

  /**
   * Navigate to student dashboard
   */
  const goToDashboard = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  /**
   * Navigate to specific student section
   */
  const goToSection = useCallback((section) => {
    navigate(`/dashboard/${section}`);
  }, [navigate]);

  /**
   * Check if current route is a student route
   */
  const isStudentRoute = useCallback(() => {
    return location.pathname.startsWith('/dashboard');
  }, [location.pathname]);

  /**
   * Get profile completion percentage
   */
  const getProfileCompletion = useCallback(() => {
    return profileCompletion;
  }, [profileCompletion]);

  /**
   * Update profile section
   */
  const updateProfileSection = useCallback((section, data) => {
    updateProfile({ [section]: data });
  }, [updateProfile]);

  /**
   * Add item to profile array section
   */
  const addToProfileSection = useCallback((section, item) => {
    const currentSection = profile[section] || [];
    updateProfile({ [section]: [...currentSection, item] });
  }, [profile, updateProfile]);

  /**
   * Remove item from profile array section
   */
  const removeFromProfileSection = useCallback((section, index) => {
    const currentSection = profile[section] || [];
    const updatedSection = currentSection.filter((_, i) => i !== index);
    updateProfile({ [section]: updatedSection });
  }, [profile, updateProfile]);

  /**
   * Update item in profile array section
   */
  const updateProfileSectionItem = useCallback((section, index, updatedItem) => {
    const currentSection = profile[section] || [];
    const updatedSection = currentSection.map((item, i) => 
      i === index ? updatedItem : item
    );
    updateProfile({ [section]: updatedSection });
  }, [profile, updateProfile]);

  /**
   * Check if job is saved
   */
  const isJobSaved = useCallback((jobId) => {
    return savedJobs.includes(jobId);
  }, [savedJobs]);

  /**
   * Toggle job save status
   */
  const toggleJobSave = useCallback(async (jobId) => {
    if (isJobSaved(jobId)) {
      return await unsaveJob(jobId);
    } else {
      return await saveJob(jobId);
    }
  }, [isJobSaved, saveJob, unsaveJob]);

  /**
   * Get application status for a job
   */
  const getApplicationStatus = useCallback((jobId) => {
    const application = applications.find(app => app.jobId === jobId);
    return application?.status || null;
  }, [applications]);

  /**
   * Check if already applied to job
   */
  const hasAppliedToJob = useCallback((jobId) => {
    return applications.some(app => app.jobId === jobId);
  }, [applications]);

  /**
   * Get test result by test ID
   */
  const getTestResult = useCallback((testId) => {
    return testResults.find(result => result.testId === testId);
  }, [testResults]);

  /**
   * Check if test is completed
   */
  const isTestCompleted = useCallback((testId) => {
    return testResults.some(result => result.testId === testId);
  }, [testResults]);

  /**
   * Get available tests (not completed)
   */
  const getAvailableTests = useCallback(() => {
    return tests.filter(test => !isTestCompleted(test.id));
  }, [tests, isTestCompleted]);

  /**
   * Get completed tests
   */
  const getCompletedTests = useCallback(() => {
    return tests.filter(test => isTestCompleted(test.id));
  }, [tests, isTestCompleted]);

  /**
   * Initialize student data
   */
  const initializeStudentData = useCallback(async () => {
    if (!isStudent) return;
    
    try {
      await Promise.all([
        fetchProfile(),
        fetchApplications(),
        fetchTests(),
        fetchTestResults(),
        fetchJobs(),
      ]);
    } catch (error) {
      console.error('Failed to initialize student data:', error);
    }
  }, [isStudent, fetchProfile, fetchApplications, fetchTests, fetchTestResults, fetchJobs]);

  return {
    // State
    profile,
    applications,
    tests,
    testResults,
    jobs,
    savedJobs,
    loading,
    error,
    profileCompletion,
    
    // Profile actions
    updateProfile,
    setProfile,
    fetchProfile,
    saveProfile,
    updateProfileSection,
    addToProfileSection,
    removeFromProfileSection,
    updateProfileSectionItem,
    getProfileCompletion,
    
    // Application actions
    fetchApplications,
    applyToJob,
    getApplicationStatus,
    hasAppliedToJob,
    
    // Test actions
    fetchTests,
    fetchTestResults,
    submitTest,
    getTestResult,
    isTestCompleted,
    getAvailableTests,
    getCompletedTests,
    
    // Job actions
    fetchJobs,
    saveJob,
    unsaveJob,
    toggleJobSave,
    isJobSaved,
    
    // Navigation helpers
    goToDashboard,
    goToSection,
    isStudentRoute,
    
    // Utility
    clearError,
    reset,
    initializeStudentData,
    
    // User info
    isStudent,
    user,
  };
};

export default useStudent;
