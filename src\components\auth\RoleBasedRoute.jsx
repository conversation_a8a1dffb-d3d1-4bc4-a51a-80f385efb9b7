import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import useAuthStore from '../../store/authStore';

/**
 * RoleBasedRoute component that handles role-based route protection
 * @param {Object} props
 * @param {React.ReactNode} props.children - The component to render if user has required role
 * @param {string|string[]} props.allowedRoles - Role(s) that can access this route
 * @param {string} props.redirectTo - Where to redirect if user doesn't have required role
 * @param {boolean} props.requireAuth - Whether authentication is required (default: true)
 */
const RoleBasedRoute = ({ 
  children, 
  allowedRoles, 
  redirectTo, 
  requireAuth = true 
}) => {
  const { user, hasRole, hasAnyRole } = useAuthStore();
  const location = useLocation();

  // First check authentication using ProtectedRoute
  return (
    <ProtectedRoute requireAuth={requireAuth}>
      <RoleChecker 
        user={user}
        allowedRoles={allowedRoles}
        redirectTo={redirectTo}
        hasRole={hasRole}
        hasAnyRole={hasAnyRole}
        location={location}
      >
        {children}
      </RoleChecker>
    </ProtectedRoute>
  );
};

/**
 * Internal component to check roles after authentication is confirmed
 */
const RoleChecker = ({ 
  children, 
  user, 
  allowedRoles, 
  redirectTo, 
  hasRole, 
  hasAnyRole, 
  location 
}) => {
  // If no roles specified, allow access (just authentication required)
  if (!allowedRoles) {
    return children;
  }

  // Convert single role to array for consistent handling
  const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

  // Check if user has any of the required roles
  const hasRequiredRole = hasAnyRole(rolesArray);

  if (!hasRequiredRole) {
    // Determine where to redirect based on user's role
    const fallbackRedirect = redirectTo || getUnauthorizedRedirect(user?.role);
    
    return (
      <Navigate 
        to={fallbackRedirect} 
        state={{ 
          from: location, 
          error: 'Access denied. Insufficient permissions.' 
        }} 
        replace 
      />
    );
  }

  return children;
};

/**
 * Get appropriate redirect path for unauthorized access based on user role
 * @param {string} userRole - Current user's role
 * @returns {string} Redirect path
 */
const getUnauthorizedRedirect = (userRole) => {
  switch (userRole) {
    case 'admin':
      return '/admin-dashboard';
    case 'company':
      return '/company-dashboard';
    case 'student':
      return '/dashboard';
    default:
      return '/';
  }
};

/**
 * Higher-order component for creating role-specific route components
 * @param {string|string[]} allowedRoles - Roles that can access routes created by this HOC
 * @returns {Function} Component that creates role-protected routes
 */
export const createRoleRoute = (allowedRoles) => {
  return ({ children, ...props }) => (
    <RoleBasedRoute allowedRoles={allowedRoles} {...props}>
      {children}
    </RoleBasedRoute>
  );
};

// Pre-configured route components for specific roles
export const AdminRoute = createRoleRoute('admin');
export const CompanyRoute = createRoleRoute('company');
export const StudentRoute = createRoleRoute('student');
export const AdminOrCompanyRoute = createRoleRoute(['admin', 'company']);

export default RoleBasedRoute;
